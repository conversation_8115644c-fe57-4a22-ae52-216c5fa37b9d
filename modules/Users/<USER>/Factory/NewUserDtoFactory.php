<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Factory;

use App\Enum\AvailableLanguagesEnum;
use App\Instance;
use App\Repositories\CompanyRepository;
use App\Repositories\GroupRepository;
use App\Repositories\InstanceRepository;
use App\Repositories\UserRepository;
use App\Rules\MainRule;
use App\Rules\SumEqualRule;
use App\Services\Language\LanguageService;
use App\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Support\Collection;
use Modules\Accounting\Pub\ViewObjects\UserAccountDimensionViewObject;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;
use Modules\Common\Factories\AbstractDtoFactory;
use Modules\Common\Rules\GooglePlaceRule;
use Modules\Users\Priv\Dtos\UserDto;
use Modules\Users\Priv\Entities\Grade;
use Modules\Users\Priv\Rules\AccountDimensionRule;
use Modules\Users\Priv\Rules\MpkRule;
use Modules\Users\Priv\Rules\PhoneNumberRule;

use Modules\Users\Priv\Rules\UserEmailRule;
use Modules\Users\Priv\Rules\UserErpIdRule;
use Modules\Users\Priv\Rules\UserHrIdRule;
use Modules\Analytics\Priv\Services\AccountDimensionService;
use Modules\Users\Priv\Services\GradeService;
use Modules\Users\Pub\Interfaces\NewUserRequestInterface;

/**
 * @extends AbstractDtoFactory<UserDto>
 */
class NewUserDtoFactory extends AbstractDtoFactory
{
    /**
     * @var Guard
     */
    protected $auth;

    /**
     * @var GradeService
     */
    protected $gradeService;

    /**
     * @var GroupRepository
     */
    protected $groupRepository;

    /**
     * @var UserRepository
     */
    protected $userRepository;

    /**
     * @var LanguageService
     */
    protected $languageService;

    protected InstanceRepository $instanceRepository;

    protected AccountDimensionRule $accountDimensionRule;
    protected AccountDimensionService $accountDimensionService;

    protected AccountDimensionItemFacade $accountDimensionItemFacade;
    protected CompanyRepository $companyRepository;

    /**
     * @param Guard $auth
     * @param GradeService $gradeService
     * @param GroupRepository $groupRepository
     * @param UserRepository $userRepository
     * @param LanguageService $languageService
     * @param InstanceRepository $instanceRepository
     * @param AccountDimensionRule $accountDimensionRule
     * @param RequiredAccountDimensionsRule $requiredAccountDimensionsRule
     * @param AccountDimensionService $accountDimensionService
     * @param AccountDimensionItemFacade $accountDimensionItemFacade
     * @param CompanyRepository $companyRepository
     */
    public function __construct(
        Guard $auth,
        GradeService $gradeService,
        GroupRepository $groupRepository,
        UserRepository $userRepository,
        LanguageService $languageService,
        InstanceRepository $instanceRepository,
        AccountDimensionRule $accountDimensionRule,
        RequiredAccountDimensionsRule $requiredAccountDimensionsRule,
        AccountDimensionService $accountDimensionService,
        AccountDimensionItemFacade $accountDimensionItemFacade,
        CompanyRepository $companyRepository
    ) {
        $this->auth = $auth;
        $this->gradeService = $gradeService;
        $this->groupRepository = $groupRepository;
        $this->userRepository = $userRepository;
        $this->languageService = $languageService;
        $this->instanceRepository = $instanceRepository;
        $this->accountDimensionRule = $accountDimensionRule;
        $this->requiredAccountDimensionsRule = $requiredAccountDimensionsRule;
        $this->accountDimensionService = $accountDimensionService;
        $this->accountDimensionItemFacade = $accountDimensionItemFacade;
        $this->companyRepository = $companyRepository;
    }

    /**
     * @param NewUserRequestInterface $request
     * @return array
     */
    protected function rules($request): array
    {
        /** @var Instance $instance */
        $instance = $this->instanceRepository->findById($request->getInstanceId());
        $instanceId = $instance->id;
        $company = $request->getCompanyId() ? $this->companyRepository->findById($request->getCompanyId()) : null;

        $availableGrades = $this->gradeService->getAvailableGrades($instance)->map(function (Grade $grade) {
            return $grade->id;
        });

        $rules = [
            $request->getFirstNameKey() => ['required', 'max:255'],
            $request->getLastNameKey() => ['required', 'max:255'],
            $request->getGroupsKey() => ['required', 'array'],
            $request->getGroupsKey() . NewUserRequestInterface::ELEMENTS_OF => [
                'required',
                'numeric',
                'exists:groups,id,instance_id,' . $instanceId
            ],
            $request->getCompanyIdKey() => ['required', 'numeric', 'exists:companies,id,instance_id,' . $instanceId],
            $request->getSexKey() => ['required', 'in:' . User::getAvailableSexes()->implode(',')],
            $request->getSupervisorIdKey() => [
                'nullable',
                'exists:users,id,instance_id,' . $instanceId
            ],
            $request->getAssistantIdKey() => [
                'nullable',
                'exists:users,id,instance_id,' . $instanceId
            ],
            $request->getMpkIdKey() => [
                'required_with:' . $request->getCompanyIdKey(),
                'nullable',
                'numeric',
                new MpkRule($instanceId, $request->getCompanyId())
            ],
            $request->getEmailKey() => [
                'required',
                'email',
                'max:255',
                new UserEmailRule($request->getEmployeeUniqueIdentifier(), $instanceId, null)
            ],
            $request->getBirthDateKey() => 'required|date',
            $request->getMpkKey() => [
                'array',
                new SumEqualRule(100, $percentageField = 'percentage'),
                new MainRule('main'),
            ],
            sprintf('%s.*.id', NewUserRequestInterface::KEY_COST_CENTERS) => [
                'required',
                'distinct',
                new MpkRule($instanceId, $request->getCompanyId())
            ],
            sprintf('%s.*.%s', NewUserRequestInterface::KEY_COST_CENTERS, $percentageField) => [
                'required',
                'numeric',
                'between:0,100'
            ],
            sprintf('%s.*.main', NewUserRequestInterface::KEY_COST_CENTERS) => ['required', 'boolean'],
            $request->getGradeIdKey() => ['required', 'numeric', 'in:' . $availableGrades->implode(',')],
            $request->getPhoneNumberKey() => ['required', new PhoneNumberRule()],
            $request->getCitizenshipIdKey() => ['required', 'numeric', 'exists:countries,id'],
            $request->getLanguageKey() => [
                'required',
                'string',
                'in:' . $this->languageService->getAvailableLanguages()->implode(',')
            ],
            $request->getWorkLocationKey() => ['nullable', 'string', new GooglePlaceRule($instance->lang, $instance->country->country_code)],
            $request->getAccountDimensionsKey() => ['nullable', 'array'],
            $request->getAccountDimensionsKey() . '.*' => [
                'nullable',
                'numeric',
                'exists:account_dimension_items,id,instance_id,' . $instanceId,
            ],
            $request->getEmployeeUniqueIdentifierKey() => ['required', 'string'],
            $request->getHrIdKey() => [
                'nullable',
                'string',
                new UserHrIdRule($request->getEmployeeUniqueIdentifier(), $instanceId, $company->id ?? null)
            ],
            $request->getErpIdKey() => [
                'nullable',
                'string',
                new UserErpIdRule($request->getEmployeeUniqueIdentifier(), $instanceId, $company->id ?? null)
            ],
        ];

        // Dodaj walidację dla wymaganych wymiarów analitycznych
        $this->addRequiredAccountDimensionsValidation($rules, $instance, $company, $request);

        return $rules;
    }

    protected function addRequiredAccountDimensionsValidation(array &$rules, Instance $instance, ?Company $company, NewUserRequestInterface $request): void
    {
        // Pobierz wszystkie wymiary analityczne dla instancji
        $allAccountDimensions = $this->accountDimensionService->getForInstance($instance);

        // Filtruj tylko wymagane wymiary
        $requiredDimensions = $allAccountDimensions->filter(function ($dimension) {
            return $dimension->is_required && $dimension->is_active;
        });

        // Dodaj walidację 'required' dla każdego wymaganego wymiaru
        foreach ($requiredDimensions as $requiredDimension) {
            $fieldName = $request->getAccountDimensionsKey() . '.' . $requiredDimension->slug;
            $rules[$fieldName] = [
                'required',
                'numeric',
                'exists:account_dimension_items,id,instance_id,' . $instance->id,
            ];
        }
    }

    protected function messages($request): array
    {
        return [
            'exists' => 'The selected :attribute is invalid (:input).',
        ];
    }

    /**
     * @param NewUserRequestInterface $request
     * @return array
     */
    protected function prepareValidationData($request): array
    {
        return [
            $request->getFirstNameKey() => $request->getFirstName(),
            $request->getLastNameKey() => $request->getLastName(),
            $request->getGroupsKey() => $request->getGroups()->toArray(),
            $request->getCompanyIdKey() => $request->getCompanyId(),
            $request->getSexKey() => $request->getSex(),
            $request->getSupervisorIdKey() => $request->getSupervisorId(),
            $request->getAssistantIdKey() => $request->getAssistantId(),
            $request->getMpkIdKey() => $request->getMpkId(),
            $request->getEmailKey() => $request->getEmail(),
            $request->getBirthDateKey() => $request->getBirthDate(),
            $request->getGradeIdKey() => $request->getGradeId(),
            $request->getPhoneNumberKey() => $request->getPhoneNumber(),
            $request->getCitizenshipIdKey() => $request->getCitizenshipId(),
            $request->getErpIdKey() => $request->getErpId(),
            $request->getHrIdKey() => $request->getHrId(),
            $request->getLanguageKey() => $request->getLanguage(),
            $request->getAccountDimensionsKey() => $request->getAccountDimensions()->toArray(),
            $request->getEmployeeUniqueIdentifierKey() => $request->getEmployeeUniqueIdentifier(),
            $request->getSlugKey() => $request->getSlug(),
            $request->getMpkKey() => $request->getMpk(),
        ];
    }

    /**
     * @param NewUserRequestInterface $request
     * @return UserDto
     */
    protected function prepare($request)
    {
        return new UserDto(
            $request->getFirstName(),
            $request->getLastName(),
            $request->getCompanyId(),
            $request->getSupervisorId(),
            $this->getAssistants($request->getAssistantId()),
            $request->getEmail(),
            $request->getSex(),
            $this->getGroups($request->getGroups()),
            $request->getCitizenshipId(),
            $request->getMpkId(),
            $request->getMpk(),
            $request->getBirthDate(),
            $request->getPhoneNumber(),
            $request->getGradeId(),
            $request->getErpId(),
            $request->getHrId(),
            new AvailableLanguagesEnum($this->languageService->getLocaleFromLanguage($request->getLanguage())),
            $request->getWorkLocation(),
            $this->getAccountDimensionItems($request->getAccountDimensions(), $request->getInstanceId()),
            $request->getEmployeeUniqueIdentifier(),
            $request->getSlug()
        );
    }

    protected function getGroups(Collection $groupIds): Collection
    {
        $result = new Collection();
        foreach ($groupIds as $groupId) {
            $result->push(
                $this->groupRepository->findById(
                    (int)$groupId
                )
            );
        }

        return $result;
    }

    protected function getAccountDimensionItems(Collection $getAccountDimensions, int $instanceId): Collection
    {
        return $getAccountDimensions->map(function ($dimensionData) use ($instanceId) {
            // Konwertuj nową strukturę danych na UserAccountDimensionViewObject
            if (is_array($dimensionData) && isset($dimensionData['account_dimension_item_id'])) {
                $dimensionViewObject = new UserAccountDimensionViewObject(
                    0, // account_dimension_id - będzie pobrane z item
                    $dimensionData['account_dimension_item_id'],
                    null // type
                );
                return $this->accountDimensionItemFacade->getByViewObject($dimensionViewObject, $instanceId);
            }

            // Fallback dla starej struktury
            if ($dimensionData instanceof UserAccountDimensionViewObject) {
                return $this->accountDimensionItemFacade->getByViewObject($dimensionData, $instanceId);
            }

            return null;
        })->filter();
    }

    protected function getAssistants(?int $assistantId): Collection
    {
        $result = new Collection();

        if (!is_null($assistantId)) {
            $result->push($this->userRepository->findUserById($assistantId));
        }

        return $result;
    }
}
